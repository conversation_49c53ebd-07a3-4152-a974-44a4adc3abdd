#!/usr/bin/env python3
"""
测试圆柱状禁飞区轨道点序生成功能
"""

import sys
import os

# 添加src路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from src.config.settings import initialize_settings
from src.core.map.map_handler_3d import Map3D
import matplotlib.pyplot as plt
import numpy as np


def test_cylindrical_orbit_path():
    """测试圆柱状禁飞区轨道点序生成"""

    # 初始化设置
    initialize_settings()

    # 创建一个较小的测试地图
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)

    # 定义测试禁飞区参数
    center_y, center_x = 100, 100  # 地图中心
    radius = 30  # 半径30个网格
    zone_name = "test_cylinder_zone"

    print(f"创建圆柱状禁飞区: 中心({center_y}, {center_x}), 半径{radius}")

    # 添加圆柱状禁飞区（这会自动生成轨道点序）
    try:
        total_points, conflicts = map3d.add_solid_cylindrical_no_fly_zone_grid(
            center_y=center_y,
            center_x=center_x,
            radius_in_grids=radius,
            zone_name=zone_name,
            planned_paths=None,
            max_height=None,
            boundary_only=True,
        )

        print(f"成功创建禁飞区，包含{total_points}个障碍物点")

    except Exception as e:
        print(f"创建禁飞区时出错: {e}")
        return False

    # 获取生成的轨道点序
    orbit_points = map3d.get_orbit_path(zone_name)

    if not orbit_points:
        print("错误：未生成轨道点序")
        return False

    print(f"生成了{len(orbit_points)}个轨道点")

    # 验证轨道点序的属性
    print("\n验证轨道点序特性:")

    # 1. 检查所有点都在地图范围内
    all_in_bounds = all(map3d.in_bounds(y, x, 0) for y, x in orbit_points)
    print(f"✓ 所有轨道点都在地图范围内: {all_in_bounds}")

    # 2. 检查轨道点与禁飞区中心的距离
    distances = []
    for y, x in orbit_points:
        dist = np.sqrt((y - center_y) ** 2 + (x - center_x) ** 2)
        distances.append(dist)

    min_dist = min(distances)
    max_dist = max(distances)
    avg_dist = np.mean(distances)

    print(
        f"✓ 轨道点距离中心: 最小{min_dist:.2f}, 最大{max_dist:.2f}, 平均{avg_dist:.2f}"
    )
    print(f"✓ 期望距离 (半径+缓冲): {radius + 3}")

    # 3. 检查轨道点是否不在禁飞区内
    orbit_in_nfz = 0
    for y, x in orbit_points:
        if not map3d.traversable(y, x, 0):  # 如果不可通行，说明在禁飞区内
            orbit_in_nfz += 1

    print(f"✓ 轨道点在禁飞区内的数量: {orbit_in_nfz} (应该为0)")

    # 可视化结果
    visualize_orbit_path(center_y, center_x, radius, orbit_points)

    print(f"\n测试完成! 轨道点序生成{'成功' if orbit_in_nfz == 0 else '需要调整'}")
    return orbit_in_nfz == 0


def visualize_orbit_path(center_y, center_x, radius, orbit_points):
    """可视化轨道点序"""

    fig, ax = plt.subplots(1, 1, figsize=(10, 10))

    # 绘制禁飞区边界（圆）
    circle = plt.Circle(
        (center_x, center_y),
        radius,
        fill=False,
        color="red",
        linewidth=2,
        label="禁飞区边界",
    )
    ax.add_patch(circle)

    # 绘制轨道点序
    if orbit_points:
        orbit_y = [point[0] for point in orbit_points]
        orbit_x = [point[1] for point in orbit_points]

        # 绘制轨道点
        ax.scatter(orbit_x, orbit_y, c="blue", s=30, alpha=0.7, label="轨道点")

        # 绘制轨道路径（连接相邻点）
        orbit_x_closed = orbit_x + [orbit_x[0]]  # 闭合路径
        orbit_y_closed = orbit_y + [orbit_y[0]]
        ax.plot(
            orbit_x_closed,
            orbit_y_closed,
            "b-",
            alpha=0.5,
            linewidth=1,
            label="轨道路径",
        )

        # 标记起点
        ax.scatter(orbit_x[0], orbit_y[0], c="green", s=100, marker="s", label="起点")

    # 绘制中心点
    ax.scatter(
        center_x, center_y, c="red", s=100, marker="x", linewidth=3, label="禁飞区中心"
    )

    # 设置坐标轴
    margin = radius + 10
    ax.set_xlim(center_x - margin, center_x + margin)
    ax.set_ylim(center_y - margin, center_y + margin)
    ax.set_aspect("equal")
    ax.grid(True, alpha=0.3)
    ax.legend()
    ax.set_title("圆柱状禁飞区轨道点序可视化")
    ax.set_xlabel("X坐标 (网格)")
    ax.set_ylabel("Y坐标 (网格)")

    plt.tight_layout()
    plt.savefig("orbit_path_visualization.png", dpi=150, bbox_inches="tight")
    plt.show()

    print("可视化图像已保存为 'orbit_path_visualization.png'")


if __name__ == "__main__":
    print("开始测试圆柱状禁飞区轨道点序生成功能...\n")
    success = test_cylindrical_orbit_path()

    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试未通过，请检查实现。")
