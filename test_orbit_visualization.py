"""
轨道点序8个象限分类功能的可视化测试
"""

import sys
import os
import math
import matplotlib.pyplot as plt
import numpy as np

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

# 导入必要的类
from src.core.map.obstacle_manager import ObstacleTypeManager


def generate_orbit_with_quadrants(
    center_y, center_x, radius_in_grids, buffer_distance=1
):
    """生成轨道点序和象限分类"""
    orbit_radius = radius_in_grids + buffer_distance
    circumference = 2 * math.pi * orbit_radius
    num_points = max(16, int(circumference * 1))

    orbit_points = []
    quadrants = {
        "N": set(),
        "NE": set(),
        "E": set(),
        "SE": set(),
        "S": set(),
        "SW": set(),
        "W": set(),
        "NW": set(),
    }

    valid_point_index = 0
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        orbit_y = center_y + orbit_radius * math.cos(angle)
        orbit_x = center_x + orbit_radius * math.sin(angle)

        grid_y = int(round(orbit_y))
        grid_x = int(round(orbit_x))

        if 0 <= grid_y < 100 and 0 <= grid_x < 100:
            orbit_points.append((grid_y, grid_x))

            # 计算角度并分配象限
            dy = grid_y - center_y
            dx = grid_x - center_x
            angle_rad = math.atan2(dx, -dy)
            angle_deg = math.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            # 象限分配
            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["N"].add(valid_point_index)
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(valid_point_index)
            elif 67.5 <= angle_deg < 112.5:
                quadrants["E"].add(valid_point_index)
            elif 112.5 <= angle_deg < 157.5:
                quadrants["SE"].add(valid_point_index)
            elif 157.5 <= angle_deg < 202.5:
                quadrants["S"].add(valid_point_index)
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(valid_point_index)
            elif 247.5 <= angle_deg < 292.5:
                quadrants["W"].add(valid_point_index)
            elif 292.5 <= angle_deg < 337.5:
                quadrants["NW"].add(valid_point_index)

            valid_point_index += 1

    return orbit_points, quadrants


def visualize_orbit_quadrants():
    """可视化轨道点序的8个象限分类"""
    center_y, center_x = 50, 50
    radius_in_grids = 15

    print(f"生成轨道点序: 中心({center_y}, {center_x}), 半径{radius_in_grids}")

    orbit_points, quadrants = generate_orbit_with_quadrants(
        center_y, center_x, radius_in_grids
    )

    print(f"生成了 {len(orbit_points)} 个轨道点")

    # 定义每个象限的颜色
    colors = {
        "N": "red",  # 正北 - 红色
        "NE": "orange",  # 东北 - 橙色
        "E": "yellow",  # 正东 - 黄色
        "SE": "green",  # 东南 - 绿色
        "S": "cyan",  # 正南 - 青色
        "SW": "blue",  # 西南 - 蓝色
        "W": "purple",  # 正西 - 紫色
        "NW": "pink",  # 西北 - 粉色
    }

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 左图：显示所有轨道点的象限分类
    ax1.set_title("轨道点序的8个象限分类", fontsize=14, fontweight="bold")

    # 绘制禁飞区（圆）
    circle = plt.Circle(
        (center_x, center_y),
        radius_in_grids,
        fill=False,
        color="black",
        linewidth=2,
        linestyle="--",
        label="禁飞区边界",
    )
    ax1.add_patch(circle)

    # 绘制轨道点，按象限着色
    for quad_name, indices in quadrants.items():
        if len(indices) > 0:
            quad_points = [orbit_points[i] for i in indices]
            y_coords = [p[0] for p in quad_points]
            x_coords = [p[1] for p in quad_points]
            ax1.scatter(
                x_coords,
                y_coords,
                c=colors[quad_name],
                s=50,
                alpha=0.8,
                label=f"{quad_name} ({len(indices)}个点)",
            )

    # 绘制中心点
    ax1.scatter(
        center_x,
        center_y,
        c="black",
        s=200,
        marker="x",
        linewidth=3,
        label="禁飞区中心",
    )

    # 添加象限分界线
    ax1.axhline(y=center_y, color="gray", linestyle=":", alpha=0.5)
    ax1.axvline(x=center_x, color="gray", linestyle=":", alpha=0.5)

    # 添加对角线
    margin = radius_in_grids + 5
    ax1.plot(
        [center_x - margin, center_x + margin],
        [center_y - margin, center_y + margin],
        color="gray",
        linestyle=":",
        alpha=0.5,
    )
    ax1.plot(
        [center_x - margin, center_x + margin],
        [center_y + margin, center_y - margin],
        color="gray",
        linestyle=":",
        alpha=0.5,
    )

    ax1.set_xlim(center_x - margin, center_x + margin)
    ax1.set_ylim(center_y - margin, center_y + margin)
    ax1.set_aspect("equal")
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    ax1.set_xlabel("X坐标 (网格)")
    ax1.set_ylabel("Y坐标 (网格)")

    # 右图：象限统计柱状图
    ax2.set_title("各象限点数统计", fontsize=14, fontweight="bold")

    quad_names = list(quadrants.keys())
    quad_counts = [len(quadrants[name]) for name in quad_names]
    quad_colors = [colors[name] for name in quad_names]

    bars = ax2.bar(quad_names, quad_counts, color=quad_colors, alpha=0.8)
    ax2.set_xlabel("象限")
    ax2.set_ylabel("点数")
    ax2.grid(True, alpha=0.3, axis="y")

    # 在柱状图上显示数值
    for bar, count in zip(bars, quad_counts):
        height = bar.get_height()
        ax2.text(
            bar.get_x() + bar.get_width() / 2.0,
            height + 0.1,
            f"{count}",
            ha="center",
            va="bottom",
            fontweight="bold",
        )

    plt.tight_layout()

    # 保存图像
    plt.savefig("orbit_quadrants_visualization.png", dpi=150, bbox_inches="tight")
    print("可视化图像已保存为 'orbit_quadrants_visualization.png'")

    # 显示图像
    plt.show()

    # 打印详细统计
    print("\n详细象限统计:")
    print("=" * 50)
    total_points = len(orbit_points)
    for quad_name in ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]:
        count = len(quadrants[quad_name])
        percentage = (count / total_points) * 100 if total_points > 0 else 0
        print(f"象限 {quad_name:2s}: {count:2d} 个点 ({percentage:5.1f}%)")
    print("=" * 50)
    print(f"总计:     {total_points:2d} 个点 (100.0%)")


def test_multiple_radii():
    """测试不同半径下的象限分布"""
    print("\n测试不同半径下的象限分布...")

    center_y, center_x = 50, 50
    radii = [5, 10, 15, 20]

    fig, axes = plt.subplots(2, 2, figsize=(12, 12))
    axes = axes.flatten()

    colors = {
        "N": "red",
        "NE": "orange",
        "E": "yellow",
        "SE": "green",
        "S": "cyan",
        "SW": "blue",
        "W": "purple",
        "NW": "pink",
    }

    for idx, radius in enumerate(radii):
        orbit_points, quadrants = generate_orbit_with_quadrants(
            center_y, center_x, radius
        )

        ax = axes[idx]
        ax.set_title(
            f"半径 {radius} 网格 ({len(orbit_points)} 个点)", fontweight="bold"
        )

        # 绘制禁飞区
        circle = plt.Circle(
            (center_x, center_y),
            radius,
            fill=False,
            color="black",
            linewidth=2,
            linestyle="--",
        )
        ax.add_patch(circle)

        # 绘制轨道点
        for quad_name, indices in quadrants.items():
            if len(indices) > 0:
                quad_points = [orbit_points[i] for i in indices]
                y_coords = [p[0] for p in quad_points]
                x_coords = [p[1] for p in quad_points]
                ax.scatter(x_coords, y_coords, c=colors[quad_name], s=30, alpha=0.8)

        # 绘制中心点
        ax.scatter(center_x, center_y, c="black", s=100, marker="x", linewidth=2)

        margin = radius + 3
        ax.set_xlim(center_x - margin, center_x + margin)
        ax.set_ylim(center_y - margin, center_y + margin)
        ax.set_aspect("equal")
        ax.grid(True, alpha=0.3)

        # 添加象限统计文本
        stats_text = ""
        for quad_name in ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]:
            count = len(quadrants[quad_name])
            stats_text += f"{quad_name}:{count} "

        ax.text(
            0.02,
            0.98,
            stats_text,
            transform=ax.transAxes,
            fontsize=8,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
        )

    plt.tight_layout()
    plt.savefig("multiple_radii_comparison.png", dpi=150, bbox_inches="tight")
    print("多半径对比图像已保存为 'multiple_radii_comparison.png'")
    plt.show()


if __name__ == "__main__":
    print("开始轨道点序8个象限分类功能的可视化测试...\n")

    try:
        # 主要可视化测试
        visualize_orbit_quadrants()

        # 多半径对比测试
        test_multiple_radii()

        print("\n🎉 所有可视化测试完成！")

    except Exception as e:
        print(f"\n❌ 可视化测试出错: {e}")
        import traceback

        traceback.print_exc()
