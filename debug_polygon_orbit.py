"""
调试多边形轨道点生成问题
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

# 导入必要的类
from src.core.map.map_handler_3d import Map3D
from src.config.settings import initialize_settings


def debug_polygon_expansion():
    """调试多边形扩展和轨道点生成"""

    # 初始化设置
    initialize_settings()

    # 创建一个测试地图
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)

    # 定义一个简单的小矩形
    polygon_coords = [
        {"lat": 39.9042, "lng": 116.4074},  # 左下角
        {"lat": 39.9042, "lng": 116.4084},  # 右下角 (更小的矩形)
        {"lat": 39.9052, "lng": 116.4084},  # 右上角
        {"lat": 39.9052, "lng": 116.4074},  # 左上角
    ]

    # 转换为网格坐标
    grid_points = np.array(
        [
            map3d.grid_converter.geo_to_relative(point["lat"], point["lng"])
            for point in polygon_coords
        ],
        dtype=np.float32,
    )

    print("原始多边形网格坐标:")
    for i, point in enumerate(grid_points):
        print(f"  点{i+1}: ({point[0]:.2f}, {point[1]:.2f})")

    # 测试多边形扩展
    polygon = grid_points[:, :2]  # 只取y,x坐标
    buffer_distance = 2  # 扩展2个网格单位

    print(f"\n扩展距离: {buffer_distance} 网格单位")

    # 调用扩展函数
    expanded_polygon = map3d._expand_polygon(polygon, buffer_distance)

    print("扩展后多边形网格坐标:")
    for i, point in enumerate(expanded_polygon):
        print(f"  点{i+1}: ({point[0]:.2f}, {point[1]:.2f})")

    # 测试轨道点生成
    print("\n开始生成轨道点...")

    # 手动生成轨道点来调试
    orbit_points = []
    n_vertices = len(expanded_polygon)

    for i in range(n_vertices):
        start_point = expanded_polygon[i]
        end_point = expanded_polygon[(i + 1) % n_vertices]

        edge_length = np.sqrt(np.sum((end_point - start_point) ** 2))
        points_on_edge = max(2, int(edge_length))

        print(
            f"边{i+1}: 从({start_point[0]:.2f}, {start_point[1]:.2f}) 到 ({end_point[0]:.2f}, {end_point[1]:.2f})"
        )
        print(f"  边长: {edge_length:.2f}, 生成点数: {points_on_edge}")

        for j in range(points_on_edge):
            t = j / points_on_edge
            point_y = start_point[0] + t * (end_point[0] - start_point[0])
            point_x = start_point[1] + t * (end_point[1] - start_point[1])

            grid_y = int(round(point_y))
            grid_x = int(round(point_x))

            # 检查是否在原始多边形内
            in_polygon = map3d._is_point_in_polygon_simple((grid_y, grid_x), polygon)

            # 检查边界（放宽检查）
            in_bounds = (
                -map3d.height <= grid_y < 2 * map3d.height
                and -map3d.width <= grid_x < 2 * map3d.width
            )

            print(
                f"    点({grid_y}, {grid_x}): 边界内={in_bounds}, 多边形内={in_polygon}"
            )

            if in_bounds and not in_polygon:
                orbit_points.append((grid_y, grid_x))

    print(f"\n总共生成 {len(orbit_points)} 个轨道点")

    # 可视化
    plt.figure(figsize=(12, 8))

    # 绘制原始多边形
    orig_y = np.append(polygon[:, 0], polygon[0, 0])
    orig_x = np.append(polygon[:, 1], polygon[0, 1])
    plt.plot(orig_x, orig_y, "b-", linewidth=2, label="原始多边形")
    plt.fill(orig_x, orig_y, color="blue", alpha=0.3)

    # 绘制扩展后的多边形
    exp_y = np.append(expanded_polygon[:, 0], expanded_polygon[0, 0])
    exp_x = np.append(expanded_polygon[:, 1], expanded_polygon[0, 1])
    plt.plot(exp_x, exp_y, "r--", linewidth=2, label="扩展后多边形")

    # 绘制轨道点
    if orbit_points:
        orbit_y = [p[0] for p in orbit_points]
        orbit_x = [p[1] for p in orbit_points]
        plt.scatter(
            orbit_x,
            orbit_y,
            c="green",
            s=50,
            alpha=0.8,
            label=f"轨道点 ({len(orbit_points)}个)",
        )

    plt.xlabel("X坐标 (网格单位)")
    plt.ylabel("Y坐标 (网格单位)")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis("equal")
    plt.title("多边形扩展和轨道点生成调试")

    plt.tight_layout()
    plt.savefig("debug_polygon_orbit.png", dpi=300, bbox_inches="tight")
    print("调试图片已保存为 debug_polygon_orbit.png")
    plt.show()


def test_point_in_polygon():
    """测试点在多边形内检测算法"""

    # 定义一个简单的正方形
    polygon = np.array([[0, 0], [0, 10], [10, 10], [10, 0]], dtype=np.float32)

    # 初始化设置
    initialize_settings()
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)

    # 测试点
    test_points = [
        (5, 5),  # 内部
        (0, 0),  # 顶点
        (5, 0),  # 边上
        (15, 5),  # 外部
        (-5, 5),  # 外部
        (5, 15),  # 外部
        (5, -5),  # 外部
    ]

    print("点在多边形内检测测试:")
    print("多边形顶点:", polygon.tolist())

    for point in test_points:
        result = map3d._is_point_in_polygon_simple(point, polygon)
        print(f"点{point}: {'内部' if result else '外部'}")


if __name__ == "__main__":
    print("=== 测试点在多边形内检测算法 ===")
    test_point_in_polygon()

    print("\n=== 调试多边形扩展和轨道点生成 ===")
    debug_polygon_expansion()
