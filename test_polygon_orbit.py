"""
测试多边形禁飞区轨道点序和象限分类功能
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

# 导入必要的类
from src.core.map.map_handler_3d import Map3D
from src.config.settings import initialize_settings


def test_polygon_orbit_generation():
    """测试多边形禁飞区轨道点序生成"""

    # 初始化设置
    initialize_settings()

    # 创建一个测试地图
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)

    # 定义一个测试多边形（矩形）
    polygon_coords = [
        {"lat": 39.9042, "lng": 116.4074},  # 左下角
        {"lat": 39.9042, "lng": 116.4174},  # 右下角
        {"lat": 39.9142, "lng": 116.4174},  # 右上角
        {"lat": 39.9142, "lng": 116.4074},  # 左上角
    ]

    zone_name = "test_polygon_zone"

    print(f"创建多边形禁飞区: {len(polygon_coords)}个顶点")

    # 添加多边形禁飞区（这会自动生成轨道点序）
    try:
        total_points, conflicts = map3d.add_solid_polygon_no_fly_zone(
            coords=polygon_coords,
            zone_name=zone_name,
            planned_paths=None,
            buffer_distance_meters=0,
        )

        print(f"成功创建禁飞区，包含{total_points}个障碍物点")

        # 获取轨道点序
        orbit_points = map3d.get_orbit_path(zone_name)
        print(f"生成了 {len(orbit_points)} 个轨道点")

        # 获取象限分类
        quadrants = map3d.get_orbit_quadrants(zone_name)
        print(f"象限分布:")

        total_quadrant_points = 0
        for quad_name, indices in quadrants.items():
            print(f"  象限 {quad_name}: {len(indices)} 个点")
            total_quadrant_points += len(indices)

            # 显示该象限的前3个点坐标
            if len(indices) > 0:
                sample_indices = list(indices)[:3]
                sample_points = [
                    orbit_points[i] for i in sample_indices if i < len(orbit_points)
                ]
                print(f"    前3个点: {sample_points}")

        print(f"总象限点数: {total_quadrant_points}, 轨道点总数: {len(orbit_points)}")

        # 验证所有轨道点都被分配到象限
        if total_quadrant_points == len(orbit_points):
            print("✓ 所有轨道点都已正确分配到象限")
        else:
            print("✗ 轨道点象限分配不完整")

        return True

    except Exception as e:
        print(f"创建禁飞区时出错: {e}")
        import traceback

        traceback.print_exc()
        return False


def visualize_polygon_orbit():
    """可视化多边形轨道点序的8个象限分类"""

    # 初始化设置
    initialize_settings()

    # 创建一个测试地图
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)

    # 定义一个更复杂的多边形（五边形）
    polygon_coords = [
        {"lat": 39.9042, "lng": 116.4074},  # 点1
        {"lat": 39.9042, "lng": 116.4174},  # 点2
        {"lat": 39.9092, "lng": 116.4224},  # 点3
        {"lat": 39.9142, "lng": 116.4124},  # 点4
        {"lat": 39.9092, "lng": 116.4024},  # 点5
    ]

    zone_name = "test_polygon_visual"

    print(f"创建多边形禁飞区用于可视化: {len(polygon_coords)}个顶点")

    try:
        # 添加多边形禁飞区
        total_points, conflicts = map3d.add_solid_polygon_no_fly_zone(
            coords=polygon_coords,
            zone_name=zone_name,
            planned_paths=None,
            buffer_distance_meters=0,
        )

        # 获取轨道点序和象限分类
        orbit_points = map3d.get_orbit_path(zone_name)
        quadrants = map3d.get_orbit_quadrants(zone_name)

        # 获取原始多边形的网格坐标
        grid_points = np.array(
            [
                map3d.grid_converter.geo_to_relative(point["lat"], point["lng"])
                for point in polygon_coords
            ],
            dtype=np.float32,
        )

        print(f"生成了 {len(orbit_points)} 个轨道点")

        # 定义每个象限的颜色
        colors = {
            "N": "red",  # 正北 - 红色
            "NE": "orange",  # 东北 - 橙色
            "E": "yellow",  # 正东 - 黄色
            "SE": "green",  # 东南 - 绿色
            "S": "cyan",  # 正南 - 青色
            "SW": "blue",  # 西南 - 蓝色
            "W": "purple",  # 正西 - 紫色
            "NW": "pink",  # 西北 - 粉色
        }

        # 创建图形
        plt.figure(figsize=(12, 10))
        plt.title("多边形禁飞区轨道点序的8个象限分类", fontsize=14, fontweight="bold")

        # 绘制原始多边形
        polygon_y = grid_points[:, 0]
        polygon_x = grid_points[:, 1]
        # 闭合多边形
        polygon_y = np.append(polygon_y, polygon_y[0])
        polygon_x = np.append(polygon_x, polygon_x[0])
        plt.plot(polygon_x, polygon_y, "k-", linewidth=2, label="禁飞区边界")
        plt.fill(polygon_x, polygon_y, color="gray", alpha=0.3, label="禁飞区内部")

        # 绘制轨道点，按象限着色
        for quad_name, indices in quadrants.items():
            if len(indices) > 0:
                quad_points = [
                    orbit_points[i] for i in indices if i < len(orbit_points)
                ]
                if quad_points:
                    y_coords = [p[0] for p in quad_points]
                    x_coords = [p[1] for p in quad_points]
                    plt.scatter(
                        x_coords,
                        y_coords,
                        c=colors[quad_name],
                        s=50,
                        alpha=0.8,
                        label=f"{quad_name} ({len(indices)}个点)",
                    )

        # 计算并绘制中心点
        center_y = np.mean(grid_points[:, 0])
        center_x = np.mean(grid_points[:, 1])
        plt.scatter(
            center_x,
            center_y,
            c="black",
            s=200,
            marker="x",
            linewidth=3,
            label="多边形中心",
        )

        plt.xlabel("X坐标 (网格单位)")
        plt.ylabel("Y坐标 (网格单位)")
        plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
        plt.grid(True, alpha=0.3)
        plt.axis("equal")
        plt.tight_layout()

        # 保存图片
        plt.savefig("polygon_orbit_quadrants.png", dpi=300, bbox_inches="tight")
        print("可视化图片已保存为 polygon_orbit_quadrants.png")

        plt.show()

        return True

    except Exception as e:
        print(f"可视化时出错: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=== 测试多边形禁飞区轨道点序生成 ===")
    success1 = test_polygon_orbit_generation()

    print("\n=== 可视化多边形轨道点序象限分类 ===")
    success2 = visualize_polygon_orbit()

    if success1 and success2:
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ 部分测试失败")
