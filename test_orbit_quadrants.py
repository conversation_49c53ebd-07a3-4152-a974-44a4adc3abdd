"""
测试轨道点序8个象限分类功能（优化版本：在生成轨道点时同时进行象限分类）
"""

import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 简单初始化设置
import sys

sys.path.insert(0, os.path.join(project_root, "src"))

# 手动创建最小配置来避免复杂的配置文件问题
from src.config.settings import Settings
from dataclasses import dataclass


@dataclass
class MinimalMapConfig:
    height: int = 100
    width: int = 100
    depth: int = 10
    boundary_thickness: int = 2
    buffer_distance_meters: int = 50


@dataclass
class MinimalConfig:
    location: str = "test"
    map: MinimalMapConfig = None

    def __post_init__(self):
        if self.map is None:
            self.map = MinimalMapConfig()


# 创建最小配置实例
from src.config import settings as settings_module

settings_module.settings = MinimalConfig()

from src.core.map.map_handler_3d import Map3D


def test_orbit_quadrants_optimized():
    """测试优化后的轨道点序8个象限分类功能"""
    print("开始测试优化的轨道点序8个象限分类功能...")

    # 创建地图实例
    map_3d = Map3D(height=100, width=100, depth=10, fixed_obstacles=False)

    # 添加一个圆柱状禁飞区
    center_y, center_x = 50, 50
    radius_in_grids = 10
    zone_name = "test_cylindrical_zone"

    print(f"添加禁飞区: 中心({center_y}, {center_x})，半径{radius_in_grids}")

    # 添加禁飞区（现在在生成轨道点时同时进行象限分类）
    total_points, conflicts = map_3d.add_solid_cylindrical_no_fly_zone_grid(
        center_y, center_x, radius_in_grids, zone_name
    )

    print(f"添加了 {total_points} 个障碍物点")

    # 获取轨道点序
    orbit_points = map_3d.get_orbit_path(zone_name)
    print(f"生成了 {len(orbit_points)} 个轨道点")

    # 获取象限分类
    quadrants = map_3d.get_orbit_quadrants(zone_name)
    print(f"轨道点序分为8个象限:")

    total_quadrant_points = 0
    for quad_name, indices in quadrants.items():
        print(f"  象限 {quad_name}: {len(indices)} 个点")
        total_quadrant_points += len(indices)

        # 获取该象限的具体坐标
        quad_points = map_3d.get_quadrant_points(zone_name, quad_name)
        if len(quad_points) > 0:
            print(f"    前3个点: {quad_points[:3]}")

    print(f"象限点总数: {total_quadrant_points}")
    print(f"轨道点总数: {len(orbit_points)}")

    # 验证象限点数量和轨道点数量一致
    assert total_quadrant_points == len(orbit_points), "象限点数量与轨道点数量不一致"

    # 验证每个象限都有点
    for quad_name in ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]:
        assert quad_name in quadrants, f"缺少象限 {quad_name}"
        # 注意：由于网格边界限制，某些象限可能没有点，所以这个检查不是必须的
        # assert len(quadrants[quad_name]) > 0, f"象限 {quad_name} 没有点"

    # 验证索引的有效性
    for quad_name, indices in quadrants.items():
        for idx in indices:
            assert (
                0 <= idx < len(orbit_points)
            ), f"象限 {quad_name} 中的索引 {idx} 超出范围"

    # 验证所有索引都被分配到某个象限
    all_indices = set()
    for indices in quadrants.values():
        all_indices.update(indices)

    expected_indices = set(range(len(orbit_points)))
    assert all_indices == expected_indices, "不是所有的轨道点都被分配到象限"

    print("✓ 所有测试通过!")

    # 测试删除功能
    print("\n测试删除禁飞区...")
    success = map_3d.remove_no_fly_zone(zone_name)
    print(f"删除结果: {success}")

    # 验证轨道点序和象限都被删除
    orbit_points_after = map_3d.get_orbit_path(zone_name)
    quadrants_after = map_3d.get_orbit_quadrants(zone_name)

    assert len(orbit_points_after) == 0, "轨道点序未被正确删除"
    assert len(quadrants_after) == 0, "象限信息未被正确删除"

    print("✓ 删除功能测试通过!")

    # 测试象限的角度分布
    print("\n测试象限角度分布...")
    test_angle_distribution(map_3d)


def test_angle_distribution(map_3d):
    """测试象限的角度分布是否正确"""
    import math

    center_y, center_x = 50, 50
    radius_in_grids = 15
    zone_name = "angle_test_zone"

    # 添加禁飞区
    map_3d.add_solid_cylindrical_no_fly_zone_grid(
        center_y, center_x, radius_in_grids, zone_name
    )

    # 获取轨道点和象限
    orbit_points = map_3d.get_orbit_path(zone_name)
    quadrants = map_3d.get_orbit_quadrants(zone_name)

    print(f"角度分布测试 - 轨道点数: {len(orbit_points)}")

    # 验证每个象限的角度范围
    angle_ranges = {
        "N": (337.5, 22.5),  # 正北，跨0度
        "NE": (22.5, 67.5),  # 东北
        "E": (67.5, 112.5),  # 正东
        "SE": (112.5, 157.5),  # 东南
        "S": (157.5, 202.5),  # 正南
        "SW": (202.5, 247.5),  # 西南
        "W": (247.5, 292.5),  # 正西
        "NW": (292.5, 337.5),  # 西北
    }

    for quad_name, indices in quadrants.items():
        if len(indices) == 0:
            continue

        min_angle, max_angle = angle_ranges[quad_name]

        for idx in indices:
            y, x = orbit_points[idx]

            # 计算角度
            dy = y - center_y
            dx = x - center_x
            angle_rad = math.atan2(dx, -dy)
            angle_deg = math.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            # 验证角度是否在正确范围内
            if quad_name == "N":  # 正北象限跨越0度
                in_range = (angle_deg >= min_angle) or (angle_deg < max_angle)
            else:
                in_range = min_angle <= angle_deg < max_angle

            if not in_range:
                print(
                    f"警告: 象限 {quad_name} 中的点 {(y, x)} 角度为 {angle_deg:.1f}°，不在期望范围内"
                )

    print("✓ 角度分布测试完成!")

    # 清理
    map_3d.remove_no_fly_zone(zone_name)


if __name__ == "__main__":
    test_orbit_quadrants_optimized()
