"""
简化的轨道点序8个象限分类功能测试
"""

import sys
import os
import math

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

# 导入必要的类
from src.core.map.obstacle_manager import ObstacleTypeManager


def test_orbit_generation_simple():
    """简化测试：直接测试轨道点序生成和象限分类逻辑"""
    print("开始简化轨道点序8个象限分类功能测试...")

    # 创建障碍物管理器
    obstacle_manager = ObstacleTypeManager(100, 100, 10)

    # 测试参数
    center_y, center_x = 50, 50
    radius_in_grids = 10
    buffer_distance = 1

    # 手动实现轨道点生成和象限分类（模拟优化后的逻辑）
    orbit_radius = radius_in_grids + buffer_distance
    circumference = 2 * math.pi * orbit_radius
    num_points = max(16, int(circumference * 1))

    print(
        f"轨道参数: 中心({center_y}, {center_x}), 轨道半径: {orbit_radius}, 点数: {num_points}"
    )

    orbit_points = []
    quadrants = {
        "N": set(),  # 正北
        "NE": set(),  # 东北
        "E": set(),  # 正东
        "SE": set(),  # 东南
        "S": set(),  # 正南
        "SW": set(),  # 西南
        "W": set(),  # 正西
        "NW": set(),  # 西北
    }

    # 生成轨道点并同时分类
    valid_point_index = 0
    for i in range(num_points):
        # 角度（从0到2π）
        angle = 2 * math.pi * i / num_points

        # 计算轨道点坐标
        orbit_y = center_y + orbit_radius * math.cos(angle)
        orbit_x = center_x + orbit_radius * math.sin(angle)

        # 转换为整数网格坐标
        grid_y = int(round(orbit_y))
        grid_x = int(round(orbit_x))

        # 检查坐标是否在地图范围内
        if 0 <= grid_y < 100 and 0 <= grid_x < 100:  # 简化的边界检查
            orbit_points.append((grid_y, grid_x))

            # 计算角度并分配到对应象限
            dy = grid_y - center_y
            dx = grid_x - center_x

            # 计算角度（弧度），然后转换为度数
            angle_rad = math.atan2(
                dx, -dy
            )  # 注意：-dy 是因为Y轴向下为正，我们要以正北为0度
            angle_deg = math.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            # 根据角度分配到对应象限
            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["N"].add(valid_point_index)
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(valid_point_index)
            elif 67.5 <= angle_deg < 112.5:
                quadrants["E"].add(valid_point_index)
            elif 112.5 <= angle_deg < 157.5:
                quadrants["SE"].add(valid_point_index)
            elif 157.5 <= angle_deg < 202.5:
                quadrants["S"].add(valid_point_index)
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(valid_point_index)
            elif 247.5 <= angle_deg < 292.5:
                quadrants["W"].add(valid_point_index)
            elif 292.5 <= angle_deg < 337.5:
                quadrants["NW"].add(valid_point_index)

            valid_point_index += 1

    # 存储到obstacle_manager
    zone_name = "test_zone"
    obstacle_manager.register_type(zone_name, "测试禁飞区")
    obstacle_manager.set_orbit_path(zone_name, orbit_points)
    obstacle_manager.set_orbit_quadrants(zone_name, quadrants)

    print(f"生成了 {len(orbit_points)} 个轨道点")
    print(f"象限分布:")

    total_quadrant_points = 0
    for quad_name, indices in quadrants.items():
        print(f"  象限 {quad_name}: {len(indices)} 个点")
        total_quadrant_points += len(indices)

        # 显示该象限的前3个点坐标
        if len(indices) > 0:
            sample_indices = list(indices)[:3]
            sample_points = [orbit_points[i] for i in sample_indices]
            print(f"    前3个点: {sample_points}")

    print(f"象限点总数: {total_quadrant_points}")
    print(f"轨道点总数: {len(orbit_points)}")

    # 验证象限点数量和轨道点数量一致
    assert total_quadrant_points == len(orbit_points), "象限点数量与轨道点数量不一致"

    # 验证每个象限都有数据结构
    for quad_name in ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]:
        assert quad_name in quadrants, f"缺少象限 {quad_name}"

    # 验证索引的有效性
    for quad_name, indices in quadrants.items():
        for idx in indices:
            assert (
                0 <= idx < len(orbit_points)
            ), f"象限 {quad_name} 中的索引 {idx} 超出范围"

    # 验证所有索引都被分配到某个象限
    all_indices = set()
    for indices in quadrants.values():
        all_indices.update(indices)

    expected_indices = set(range(len(orbit_points)))
    assert all_indices == expected_indices, "不是所有的轨道点都被分配到象限"

    print("✓ 所有测试通过!")

    # 测试获取功能
    print("\n测试数据获取功能...")
    retrieved_orbit = obstacle_manager.get_orbit_path(zone_name)
    retrieved_quadrants = obstacle_manager.get_orbit_quadrants(zone_name)

    assert len(retrieved_orbit) == len(orbit_points), "获取的轨道点数量不匹配"
    assert len(retrieved_quadrants) == len(quadrants), "获取的象限数量不匹配"

    # 测试指定象限的点获取
    for quad_name in quadrants.keys():
        quad_points = obstacle_manager.get_quadrant_points(zone_name, quad_name)
        expected_count = len(quadrants[quad_name])
        assert len(quad_points) == expected_count, f"象限 {quad_name} 的点数量不匹配"

    print("✓ 数据获取功能测试通过!")

    # 测试删除功能
    print("\n测试删除功能...")
    removed_orbit = obstacle_manager.remove_orbit_path(zone_name)

    assert len(removed_orbit) == len(orbit_points), "删除返回的轨道点数量不匹配"

    # 验证删除后获取为空
    after_removal_orbit = obstacle_manager.get_orbit_path(zone_name)
    after_removal_quadrants = obstacle_manager.get_orbit_quadrants(zone_name)

    assert len(after_removal_orbit) == 0, "删除后轨道点未清空"
    assert len(after_removal_quadrants) == 0, "删除后象限信息未清空"

    print("✓ 删除功能测试通过!")

    return True


def test_angle_calculation():
    """测试角度计算的正确性"""
    print("\n测试角度计算...")

    center_y, center_x = 50, 50

    # 测试各个方向的角度计算
    test_points = [
        ((40, 50), "N", 0),  # 正北
        ((40, 60), "NE", 45),  # 东北
        ((50, 60), "E", 90),  # 正东
        ((60, 60), "SE", 135),  # 东南
        ((60, 50), "S", 180),  # 正南
        ((60, 40), "SW", 225),  # 西南
        ((50, 40), "W", 270),  # 正西
        ((40, 40), "NW", 315),  # 西北
    ]

    for (y, x), expected_quad, expected_angle in test_points:
        dy = y - center_y
        dx = x - center_x

        angle_rad = math.atan2(dx, -dy)
        angle_deg = math.degrees(angle_rad)
        if angle_deg < 0:
            angle_deg += 360

        print(
            f"点({y}, {x}) -> 角度: {angle_deg:.1f}°, 期望: {expected_angle}°, 期望象限: {expected_quad}"
        )

        # 验证角度在合理范围内
        angle_diff = abs(angle_deg - expected_angle)
        if angle_diff > 180:  # 处理跨0度的情况
            angle_diff = 360 - angle_diff

        assert angle_diff < 10, f"角度计算偏差过大: {angle_diff}°"

    print("✓ 角度计算测试通过!")


if __name__ == "__main__":
    print("开始简化的轨道点序8个象限分类功能测试...\n")

    try:
        test_orbit_generation_simple()
        test_angle_calculation()
        print("\n🎉 所有测试都通过了！")
        print("\n测试总结:")
        print("✓ 轨道点序生成功能正常")
        print("✓ 8个象限分类逻辑正确")
        print("✓ 数据存储和获取功能正常")
        print("✓ 删除功能正常")
        print("✓ 角度计算准确")

    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback

        traceback.print_exc()
