"""
简单的多边形轨道点生成测试（使用网格坐标）
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

# 导入必要的类
from src.core.map.map_handler_3d import Map3D
from src.config.settings import initialize_settings


def test_simple_polygon_orbit():
    """使用简单的网格坐标测试多边形轨道点生成"""
    
    # 初始化设置
    initialize_settings()
    
    # 创建一个测试地图
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)
    
    # 定义一个简单的矩形（网格坐标）
    polygon = np.array([
        [50, 50],   # 左下角
        [50, 80],   # 右下角
        [80, 80],   # 右上角
        [80, 50],   # 左上角
    ], dtype=np.float32)
    
    print("原始多边形网格坐标:")
    for i, point in enumerate(polygon):
        print(f"  点{i+1}: ({point[0]:.2f}, {point[1]:.2f})")
    
    # 直接调用轨道点生成函数
    print("\n开始生成轨道点...")
    orbit_points, quadrants = map3d._generate_polygon_orbit_path(polygon, buffer_distance=2)
    
    print(f"生成了 {len(orbit_points)} 个轨道点")
    print(f"象限分布:")
    
    total_quadrant_points = 0
    for quad_name, indices in quadrants.items():
        print(f"  象限 {quad_name}: {len(indices)} 个点")
        total_quadrant_points += len(indices)
        
        # 显示该象限的前3个点坐标
        if len(indices) > 0:
            sample_indices = list(indices)[:3]
            sample_points = [orbit_points[i] for i in sample_indices if i < len(orbit_points)]
            print(f"    前3个点: {sample_points}")
    
    print(f"总象限点数: {total_quadrant_points}, 轨道点总数: {len(orbit_points)}")
    
    # 验证所有轨道点都被分配到象限
    if total_quadrant_points == len(orbit_points):
        print("✓ 所有轨道点都已正确分配到象限")
    else:
        print("✗ 轨道点象限分配不完整")
    
    # 可视化
    plt.figure(figsize=(12, 10))
    plt.title("简单多边形轨道点序的8个象限分类", fontsize=14, fontweight="bold")
    
    # 绘制原始多边形
    polygon_y = np.append(polygon[:, 0], polygon[0, 0])
    polygon_x = np.append(polygon[:, 1], polygon[0, 1])
    plt.plot(polygon_x, polygon_y, 'k-', linewidth=2, label="禁飞区边界")
    plt.fill(polygon_x, polygon_y, color='gray', alpha=0.3, label="禁飞区内部")
    
    # 绘制扩展后的多边形
    expanded_polygon = map3d._expand_polygon(polygon, 2)
    exp_y = np.append(expanded_polygon[:, 0], expanded_polygon[0, 0])
    exp_x = np.append(expanded_polygon[:, 1], expanded_polygon[0, 1])
    plt.plot(exp_x, exp_y, 'r--', linewidth=2, label="扩展后边界")
    
    # 定义每个象限的颜色
    colors = {
        "N": "red",     # 正北 - 红色
        "NE": "orange", # 东北 - 橙色
        "E": "yellow",  # 正东 - 黄色
        "SE": "green",  # 东南 - 绿色
        "S": "cyan",    # 正南 - 青色
        "SW": "blue",   # 西南 - 蓝色
        "W": "purple",  # 正西 - 紫色
        "NW": "pink",   # 西北 - 粉色
    }
    
    # 绘制轨道点，按象限着色
    for quad_name, indices in quadrants.items():
        if len(indices) > 0:
            quad_points = [orbit_points[i] for i in indices if i < len(orbit_points)]
            if quad_points:
                y_coords = [p[0] for p in quad_points]
                x_coords = [p[1] for p in quad_points]
                plt.scatter(
                    x_coords, y_coords,
                    c=colors[quad_name],
                    s=50,
                    alpha=0.8,
                    label=f"{quad_name} ({len(indices)}个点)"
                )
    
    # 计算并绘制中心点
    center_y = np.mean(polygon[:, 0])
    center_x = np.mean(polygon[:, 1])
    plt.scatter(center_x, center_y, c="black", s=200, marker="x", linewidth=3, label="多边形中心")
    
    plt.xlabel("X坐标 (网格单位)")
    plt.ylabel("Y坐标 (网格单位)")
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.tight_layout()
    
    # 保存图片
    plt.savefig("simple_polygon_orbit.png", dpi=300, bbox_inches='tight')
    print("可视化图片已保存为 simple_polygon_orbit.png")
    
    plt.show()
    
    return len(orbit_points) > 0


def test_different_shapes():
    """测试不同形状的多边形"""
    
    # 初始化设置
    initialize_settings()
    
    # 创建一个测试地图
    map3d = Map3D(height=200, width=200, depth=20, fixed_obstacles=False)
    
    # 定义不同形状的多边形
    shapes = {
        "三角形": np.array([
            [60, 60],
            [60, 90],
            [90, 75],
        ], dtype=np.float32),
        
        "五边形": np.array([
            [100, 100],
            [100, 130],
            [115, 140],
            [130, 130],
            [130, 100],
        ], dtype=np.float32),
        
        "L形": np.array([
            [140, 140],
            [140, 170],
            [155, 170],
            [155, 155],
            [170, 155],
            [170, 140],
        ], dtype=np.float32),
    }
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for idx, (shape_name, polygon) in enumerate(shapes.items()):
        ax = axes[idx]
        
        print(f"\n=== 测试 {shape_name} ===")
        
        # 生成轨道点
        orbit_points, quadrants = map3d._generate_polygon_orbit_path(polygon, buffer_distance=2)
        
        print(f"生成了 {len(orbit_points)} 个轨道点")
        
        # 绘制原始多边形
        polygon_y = np.append(polygon[:, 0], polygon[0, 0])
        polygon_x = np.append(polygon[:, 1], polygon[0, 1])
        ax.plot(polygon_x, polygon_y, 'k-', linewidth=2, label="禁飞区边界")
        ax.fill(polygon_x, polygon_y, color='gray', alpha=0.3)
        
        # 绘制轨道点
        colors = ["red", "orange", "yellow", "green", "cyan", "blue", "purple", "pink"]
        color_idx = 0
        for quad_name, indices in quadrants.items():
            if len(indices) > 0:
                quad_points = [orbit_points[i] for i in indices if i < len(orbit_points)]
                if quad_points:
                    y_coords = [p[0] for p in quad_points]
                    x_coords = [p[1] for p in quad_points]
                    ax.scatter(x_coords, y_coords, c=colors[color_idx], s=30, alpha=0.8)
                    color_idx = (color_idx + 1) % len(colors)
        
        # 绘制中心点
        center_y = np.mean(polygon[:, 0])
        center_x = np.mean(polygon[:, 1])
        ax.scatter(center_x, center_y, c="black", s=100, marker="x", linewidth=2)
        
        ax.set_title(f"{shape_name} ({len(orbit_points)}个轨道点)")
        ax.set_xlabel("X坐标")
        ax.set_ylabel("Y坐标")
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
    
    plt.tight_layout()
    plt.savefig("different_shapes_orbit.png", dpi=300, bbox_inches='tight')
    print("\n不同形状的可视化图片已保存为 different_shapes_orbit.png")
    plt.show()


if __name__ == "__main__":
    print("=== 测试简单多边形轨道点生成 ===")
    success1 = test_simple_polygon_orbit()
    
    print("\n=== 测试不同形状的多边形 ===")
    test_different_shapes()
    
    if success1:
        print("\n✓ 测试通过！")
    else:
        print("\n✗ 测试失败")
